<?php
// 测试标签搜索重写规则修复
echo "<h1>标签搜索重写规则修复验证</h1>";

echo "<h2>问题分析</h2>";
echo "<div style='background:#fff3cd; padding:15px; border:1px solid #ffc107; border-radius:5px;'>";
echo "<p><strong>问题URL:</strong></p>";
echo "<code>https://www.95dir.com/search/tags-emulator+games%E3%80%81retro+games%E3%80%81%E5%A4%8D%E5%8F%A4%E6%B8%B8%E6%88%8F%E3%80%81%E7%BB%8F%E5%85%B8%E6%B8%B8%E6%88%8F-1.html</code>";
echo "<p><strong>URL解码后:</strong></p>";
echo "<code>/search/tags-emulator games、retro games、复古游戏、经典游戏-1.html</code>";
echo "<p><strong>URL格式:</strong> <code>search/{type}-{query}-{page}.html</code></p>";
echo "</div>";

echo "<h2>修复方案</h2>";
echo "<div style='background:#d4edda; padding:15px; border:1px solid #c3e6cb; border-radius:5px;'>";
echo "<p>在 <code>httpd.ini</code> 中添加了对混合格式的支持：</p>";
echo "<pre style='background:#f8f9fa; padding:10px; border:1px solid #dee2e6;'>";
echo "# 格式4: search/{type}-{query}-{page}.html (混合格式)\n";
echo "RewriteRule ^/search\\/(name|url|tags|intro|br|pr|art|article)-(.+)-(\\d+)\\.html$ /index.php\\?mod=search&type=\$1&query=\$2&page=\$3\n";
echo "RewriteRule ^/search\\/(name|url|tags|intro|br|pr|art|article)-(.+)\\.html$ /index.php\\?mod=search&type=\$1&query=\$2";
echo "</pre>";
echo "</div>";

echo "<h2>支持的搜索URL格式</h2>";
echo "<table border='1' cellpadding='8' cellspacing='0' style='border-collapse:collapse; width:100%;'>";
echo "<tr style='background:#f8f9fa;'><th>格式</th><th>示例URL</th><th>说明</th></tr>";
echo "<tr><td>格式1</td><td><code>search-tags-游戏-1.html</code></td><td>完全连字符格式</td></tr>";
echo "<tr><td>格式2</td><td><code>search/tags/游戏-1.html</code></td><td>斜杠分隔type，连字符分隔page</td></tr>";
echo "<tr><td>格式3</td><td><code>br/游戏-1.html</code></td><td>简化格式（仅br/pr）</td></tr>";
echo "<tr style='background:#fff3cd;'><td>格式4</td><td><code>search/tags-游戏-1.html</code></td><td><strong>新增：混合格式（修复目标）</strong></td></tr>";
echo "</table>";

echo "<h2>测试链接</h2>";
echo "<p>以下是各种格式的测试链接：</p>";

$test_cases = array(
    // 原问题URL（简化版本）
    '/search/tags-游戏-1.html' => '混合格式 - 标签搜索带分页',
    '/search/tags-游戏.html' => '混合格式 - 标签搜索不带分页',
    
    // 其他类型测试
    '/search/name-测试网站-2.html' => '混合格式 - 网站名称搜索',
    '/search/url-example-1.html' => '混合格式 - URL搜索',
    '/search/intro-描述内容-3.html' => '混合格式 - 介绍搜索',
    '/search/article-教程-1.html' => '混合格式 - 文章搜索',
    
    // 对比其他格式
    '/search-tags-游戏-1.html' => '格式1 - 完全连字符',
    '/search/tags/游戏-1.html' => '格式2 - 标准格式',
    '/br/游戏-1.html' => '格式3 - 简化格式'
);

echo "<table border='1' cellpadding='8' cellspacing='0' style='border-collapse:collapse; width:100%;'>";
echo "<tr style='background:#f8f9fa;'><th>URL</th><th>说明</th><th>测试</th></tr>";
foreach ($test_cases as $url => $desc) {
    $highlight = (strpos($url, '/search/tags-') === 0 || strpos($url, '/search/name-') === 0 || strpos($url, '/search/url-') === 0 || strpos($url, '/search/intro-') === 0 || strpos($url, '/search/article-') === 0) ? 'background:#fff3cd;' : '';
    echo "<tr style='$highlight'>";
    echo "<td><code>" . htmlspecialchars($url) . "</code></td>";
    echo "<td>" . $desc . "</td>";
    echo "<td><a href='" . htmlspecialchars($url) . "' target='_blank'>测试</a></td>";
    echo "</tr>";
}
echo "</table>";

echo "<h2>URL解析测试</h2>";
echo "<p>原问题URL的解析过程：</p>";
echo "<div style='background:#f8f9fa; padding:15px; border:1px solid #dee2e6; border-radius:5px;'>";
echo "<p><strong>输入URL:</strong> <code>/search/tags-emulator+games、retro+games、复古游戏、经典游戏-1.html</code></p>";
echo "<p><strong>匹配规则:</strong> <code>^/search\\/(tags)-(.+)-(\\d+)\\.html$</code></p>";
echo "<p><strong>解析结果:</strong></p>";
echo "<ul>";
echo "<li><code>\$1</code> = <code>tags</code> (搜索类型)</li>";
echo "<li><code>\$2</code> = <code>emulator+games、retro+games、复古游戏、经典游戏</code> (搜索关键词)</li>";
echo "<li><code>\$3</code> = <code>1</code> (页码)</li>";
echo "</ul>";
echo "<p><strong>转换为:</strong> <code>/index.php?mod=search&type=tags&query=emulator+games、retro+games、复古游戏、经典游戏&page=1</code></p>";
echo "</div>";

// 如果当前请求是搜索请求，显示解析结果
if (isset($_GET['mod']) && $_GET['mod'] == 'search') {
    echo "<h2>当前请求解析结果</h2>";
    echo "<div style='background:#d1ecf1; padding:15px; border:1px solid #bee5eb; border-radius:5px;'>";
    echo "<p><strong>✅ 重写规则工作正常！</strong></p>";
    echo "<ul>";
    echo "<li><strong>模块:</strong> " . htmlspecialchars($_GET['mod']) . "</li>";
    if (isset($_GET['type'])) echo "<li><strong>搜索类型:</strong> " . htmlspecialchars($_GET['type']) . "</li>";
    if (isset($_GET['query'])) echo "<li><strong>搜索关键词:</strong> " . htmlspecialchars($_GET['query']) . "</li>";
    if (isset($_GET['page'])) echo "<li><strong>页码:</strong> " . htmlspecialchars($_GET['page']) . "</li>";
    echo "</ul>";
    
    if (isset($_GET['type']) && $_GET['type'] == 'tags') {
        echo "<p><strong>🎯 标签搜索修复成功！</strong></p>";
    }
    echo "</div>";
}

echo "<h2>注意事项</h2>";
echo "<div style='background:#f8d7da; padding:15px; border:1px solid #f5c6cb; border-radius:5px;'>";
echo "<p><strong>重要提醒:</strong></p>";
echo "<ul>";
echo "<li>此修复只修改了 <code>httpd.ini</code> 重写规则，未修改任何PHP文件</li>";
echo "<li>保持了与现有默认链接规则的兼容性</li>";
echo "<li>支持中文、英文、特殊字符的搜索关键词</li>";
echo "<li>支持URL编码的关键词（如 %E3%80%81 等）</li>";
echo "</ul>";
echo "</div>";

echo "<hr>";
echo "<p><small>修复时间: " . date('Y-m-d H:i:s') . "</small></p>";
?>
