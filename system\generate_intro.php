<?php
// generate_intro.php
header('Content-Type: application/json');

// 引入系统配置
define('IN_ADMIN', TRUE);
define('ROOT_PATH', str_replace('\\', '/', dirname(dirname(__FILE__))).'/');
define('APP_PATH', ROOT_PATH.'source/');
require(APP_PATH.'init.php');
require(APP_PATH.'module/static.php');

// 获取提交的数据和操作类型
$action = isset($_POST['action']) ? trim($_POST['action']) : 'generate_intro'; // 默认生成简介
$url = isset($_POST['url']) ? trim($_POST['url']) : '';
$tags = isset($_POST['tags']) ? trim($_POST['tags']) : '';
$intro = isset($_POST['intro']) ? trim($_POST['intro']) : '';
$meta_title = isset($_POST['meta_title']) ? trim($_POST['meta_title']) : '';
$meta_keywords = isset($_POST['meta_keywords']) ? trim($_POST['meta_keywords']) : '';
$meta_description = isset($_POST['meta_description']) ? trim($_POST['meta_description']) : '';

// 根据不同操作类型验证必需参数
if ($action == 'generate_keywords_intro') {
    // 生成关键词和简介需要URL
    if (empty($url)) {
        echo json_encode(array('status' => 'error', 'message' => '请输入网站域名'));
        exit;
    }
} else {
    // 原有的生成AI简介功能
    if (empty($url) || empty($tags) || empty($intro)) {
        echo json_encode(array('status' => 'error', 'message' => '请填写网站域名、TAG标签和网站简介'));
        exit;
    }
}

// 智谱AI API配置 - 使用您提供的API Key
$api_key = '47104bcce99c4822974db0bc9ed4f2eb.McDW4LZcouOC8BVv';
$api_url = 'https://open.bigmodel.cn/api/paas/v4/chat/completions';

// 根据操作类型构建不同的Prompt
if ($action == 'generate_keywords_intro') {
    // 构建网站信息
    $site_info = "网站域名：{$url}\n";
    if (!empty($meta_title)) {
        $site_info .= "网站标题：{$meta_title}\n";
    }
    if (!empty($meta_keywords)) {
        $site_info .= "Meta关键词：{$meta_keywords}\n";
    }
    if (!empty($meta_description)) {
        $site_info .= "Meta描述：{$meta_description}\n";
    }

    // 生成关键词和简介的Prompt
    $prompt = "你是一个专业的SEO和网站内容分析专家。请根据以下网站信息，为网站生成合适的关键词和简介：\n\n" .
              $site_info . "\n" .
              "要求：\n" .
              "1. 根据域名和已有信息分析网站可能的主题和功能\n" .
              "2. 生成符合SEO规范的关键词\n" .
              "3. 简介要突出网站的价值和特色\n\n" .
              "请按以下格式输出（严格按照格式，不要添加其他内容）：\n" .
              "关键词：[生成3-5个相关关键词，用英文逗号分隔]\n" .
              "简介：[生成一段50-200字的网站简介，突出网站特色和价值]";

    $model = 'glm-4.5-air'; // 使用您指定的模型
} else {
    // 原有的生成AI简介功能
    $prompt_template = isset($options['ai_prompt_template']) && !empty($options['ai_prompt_template']) ? $options['ai_prompt_template'] :
        "你是一个专业的网站内容撰写助手，请根据以下信息为网站生成一段简洁、有吸引力的AI简介（输出600字左右的HTML内容）：\n" .
        "使用<p><ul><li><strong>等基础标签\n" .
        "包含SEO关键词但保持自然\n" .
        "分3-5个段落，每个段落有明确主题\n" .
        "符合中文阅读习惯\n" .
        "请确保内容突出网站特色，语言流畅自然，适合用作网站AI简介\n" .
        "只要一个文本网址不要链接";

    $prompt = $prompt_template . "\n" .
              "网站域名：{$url}\n" .
              "TAG标签：{$tags}\n" .
              "网站简介：{$intro}\n";

    $model = isset($options['ai_model_name']) && !empty($options['ai_model_name']) ? $options['ai_model_name'] : 'glm-4';
}

// 请求智谱AI API
$request_data = array(
    'model' => $model,
    'messages' => array(
        array('role' => 'user', 'content' => $prompt)
    ),
    'temperature' => 0.7
);

$ch = curl_init($api_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 60); // 60秒超时
curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10); // 10秒连接超时
curl_setopt($ch, CURLOPT_HTTPHEADER, array(
    'Content-Type: application/json',
    'Authorization: Bearer ' . $api_key
));
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($request_data));

$response = curl_exec($ch);
$curl_error = curl_error($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($curl_error) {
    if (strpos($curl_error, 'timeout') !== false) {
        echo json_encode(array('status' => 'error', 'message' => '生成超时，请尝试使用更简洁的提示词模板'));
    } else {
        echo json_encode(array('status' => 'error', 'message' => 'API请求失败：' . $curl_error));
    }
    exit;
}

if ($http_code === 524) {
    echo json_encode(array('status' => 'error', 'message' => '生成超时(524)，请尝试使用更简洁的提示词模板'));
    exit;
}

if ($http_code !== 200) {
    echo json_encode(array('status' => 'error', 'message' => 'HTTP错误：' . $http_code));
    exit;
}

// 解析智谱AI API的响应
$response_data = json_decode($response, true);
if (isset($response_data['choices'][0]['message']['content'])) {
    $generated_content = $response_data['choices'][0]['message']['content'];

    if ($action == 'generate_keywords_intro') {
        // 解析关键词和简介
        $lines = explode("\n", $generated_content);
        $keywords = '';
        $intro = '';

        foreach ($lines as $line) {
            $line = trim($line);
            if (strpos($line, '关键词：') === 0) {
                $keywords = trim(str_replace('关键词：', '', $line));
            } elseif (strpos($line, '简介：') === 0) {
                $intro = trim(str_replace('简介：', '', $line));
            }
        }

        echo json_encode(array(
            'status' => 'success',
            'message' => '生成成功',
            'keywords' => $keywords,
            'intro' => $intro,
            'raw_content' => $generated_content
        ));
    } else {
        // 原有的AI简介生成
        echo json_encode(array('status' => 'success', 'message' => '生成成功', 'content' => $generated_content));
    }
} else {
    echo json_encode(array('status' => 'error', 'message' => 'API响应异常：' . json_encode($response_data)));
}

?>
