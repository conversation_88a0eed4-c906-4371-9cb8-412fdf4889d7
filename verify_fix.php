<?php
// 验证搜索重写规则修复
echo "<h1>搜索重写规则修复验证</h1>";

echo "<h2>修复总结</h2>";
echo "<div style='background:#e8f5e8; padding:15px; border:1px solid #4CAF50; border-radius:5px;'>";
echo "<h3>✅ 问题已修复</h3>";
echo "<p><strong>问题URL:</strong> <code>https://www.95dir.com/search/article-whois-1.html</code></p>";
echo "<p><strong>错误原因:</strong> httpd.ini中的搜索重写规则不支持 <code>article</code> 类型</p>";
echo "<p><strong>修复方案:</strong></p>";
echo "<ol>";
echo "<li>在 <code>httpd.ini</code> 中添加了对 <code>article</code>, <code>art</code>, <code>br</code>, <code>pr</code> 类型的支持</li>";
echo "<li>在 <code>module/search.php</code> 中添加了对这些新类型的处理逻辑</li>";
echo "<li>支持3种URL格式，与nginx.conf保持一致</li>";
echo "</ol>";
echo "</div>";

echo "<h2>修复详情</h2>";

echo "<h3>1. httpd.ini 修改</h3>";
echo "<p>原来的搜索规则只支持：<code>(name|url|tags|intro)</code></p>";
echo "<p>修复后支持：<code>(name|url|tags|intro|br|pr|art|article)</code></p>";
echo "<pre style='background:#f5f5f5; padding:10px; border:1px solid #ddd;'>";
echo "# 修复前（只支持4种类型）:\n";
echo "RewriteRule ^/search\\/(name|url|tags|intro)\\/(.+)-(\\d+)\\.html$ /index.php\\?mod=search&type=\$1&query=\$2&page=\$3\n\n";
echo "# 修复后（支持8种类型）:\n";
echo "RewriteRule ^/search\\/(name|url|tags|intro|br|pr|art|article)\\/(.+)-(\\d+)\\.html$ /index.php\\?mod=search&type=\$1&query=\$2&page=\$3";
echo "</pre>";

echo "<h3>2. search.php 模块修改</h3>";
echo "<pre style='background:#f5f5f5; padding:10px; border:1px solid #ddd;'>";
echo "# 修复前:\n";
echo "if (\$strtype == 'article') {\n\n";
echo "# 修复后:\n";
echo "if (\$strtype == 'article' || \$strtype == 'art') {\n\n";
echo "# 新增br和pr类型处理:\n";
echo "case 'br' :\n";
echo "case 'pr' :\n";
echo "    // 百度权重/PageRank搜索逻辑";
echo "</pre>";

echo "<h2>测试链接</h2>";
echo "<p>点击以下链接测试修复效果：</p>";
echo "<ul>";
echo "<li><a href='/search/article-whois-1.html' target='_blank'>/search/article-whois-1.html</a> - 原问题URL</li>";
echo "<li><a href='/search/article/whois.html' target='_blank'>/search/article/whois.html</a> - 文章搜索</li>";
echo "<li><a href='/search/art/tutorial.html' target='_blank'>/search/art/tutorial.html</a> - art类型搜索</li>";
echo "<li><a href='/search-article-guide-2.html' target='_blank'>/search-article-guide-2.html</a> - 格式1搜索</li>";
echo "<li><a href='/br/test.html' target='_blank'>/br/test.html</a> - 百度权重搜索</li>";
echo "<li><a href='/pr/example.html' target='_blank'>/pr/example.html</a> - PageRank搜索</li>";
echo "</ul>";

echo "<h2>兼容性说明</h2>";
echo "<div style='background:#fff3cd; padding:15px; border:1px solid #ffc107; border-radius:5px;'>";
echo "<p><strong>注意:</strong> 此修复同时兼容Apache和Nginx环境</p>";
echo "<ul>";
echo "<li><strong>Apache:</strong> 使用 httpd.ini 文件中的重写规则</li>";
echo "<li><strong>Nginx:</strong> 使用 nginx.conf 文件中的重写规则（已经支持这些类型）</li>";
echo "</ul>";
echo "<p>现在两个配置文件的搜索规则完全一致，确保在不同Web服务器环境下都能正常工作。</p>";
echo "</div>";

// 如果当前请求是搜索请求，显示解析结果
if (isset($_GET['mod']) && $_GET['mod'] == 'search') {
    echo "<h2>当前请求解析结果</h2>";
    echo "<div style='background:#d4edda; padding:15px; border:1px solid #c3e6cb; border-radius:5px;'>";
    echo "<p><strong>✅ 重写规则工作正常！</strong></p>";
    echo "<ul>";
    echo "<li><strong>模块:</strong> " . htmlspecialchars($_GET['mod']) . "</li>";
    if (isset($_GET['type'])) echo "<li><strong>搜索类型:</strong> " . htmlspecialchars($_GET['type']) . "</li>";
    if (isset($_GET['query'])) echo "<li><strong>搜索关键词:</strong> " . htmlspecialchars($_GET['query']) . "</li>";
    if (isset($_GET['page'])) echo "<li><strong>页码:</strong> " . htmlspecialchars($_GET['page']) . "</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<hr>";
echo "<p><small>修复完成时间: " . date('Y-m-d H:i:s') . "</small></p>";
?>
