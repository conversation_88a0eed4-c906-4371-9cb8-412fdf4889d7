# 重定向查询参数格式到伪静态格式（SEO优化）
# 详情页面重定向（必须在基础模块之前，避免被错误匹配）
if ($args ~ "^mod=siteinfo&wid=([0-9]+)$") {
    return 301 /siteinfo-$1.html;
}
if ($args ~ "^mod=artinfo&aid=([0-9]+)$") {
    return 301 /artinfo-$1.html;
}
if ($args ~ "^mod=linkinfo&lid=([0-9]+)$") {
    return 301 /linkinfo-$1.html;
}
if ($args ~ "^mod=diypage&pid=([0-9]+)$") {
    return 301 /diypage-$1.html;
}
if ($args ~ "^mod=(pending_detail|blacklist_detail|rejected_detail|vip_detail)&id=([0-9]+)$") {
    return 301 /$1-$2.html;
}

# 基础模块重定向（放在详情页面之后）
if ($args ~ "^mod=(index|webdir|weblink|article|category|update|archives|top|feedback|link|rssfeed|sitemap|pending|blacklist|rejected|vip_list|datastats)$") {
    return 301 /$1.html;
}
if ($args ~ "^mod=(pending|blacklist|rejected|vip_list)&page=([0-9]+)$") {
    return 301 /$1-$2.html;
}
if ($args ~ "^mod=webdir&cid=([0-9]+)&page=([0-9]+)$") {
    return 301 /webdir-$1-$2.html;
}
if ($args ~ "^mod=webdir&cid=([0-9]+)$") {
    return 301 /webdir-$1.html;
}
if ($args ~ "^mod=weblink&cid=([0-9]+)&page=([0-9]+)$") {
    return 301 /weblink-$1-$2.html;
}
if ($args ~ "^mod=weblink&cid=([0-9]+)$") {
    return 301 /weblink-$1.html;
}
if ($args ~ "^mod=article&cid=([0-9]+)&page=([0-9]+)$") {
    return 301 /article-$1-$2.html;
}
if ($args ~ "^mod=article&cid=([0-9]+)$") {
    return 301 /article-$1.html;
}

# 处理查询参数中的mod参数（解决相对路径问题，不重定向）
# 注意：这个规则会匹配所有未被上面重定向规则处理的查询参数
if ($args ~ "^mod=(.+)") {
    rewrite ^.*$ /index.php?mod=$1 last;
}

# 网站详情 - 支持所有4种链接结构格式
rewrite ^/siteinfo-(\d+)-(.+)\.html$ /index.php?mod=siteinfo&wid=$1;
rewrite ^/siteinfo-(\d+)\.html$ /index.php?mod=siteinfo&wid=$1;
rewrite ^/siteinfo/(\d+)-(.+)\.html$ /index.php?mod=siteinfo&wid=$1;
rewrite ^/siteinfo/(\d+)\.html$ /index.php?mod=siteinfo&wid=$1;
rewrite ^/siteinfo/(\d+)/(.+)/(.+)/?$ /index.php?mod=siteinfo&wid=$1;
rewrite ^/siteinfo/(\d+)/(.+)/?$ /index.php?mod=siteinfo&wid=$1;
rewrite ^/siteinfo/(\d+)/?$ /index.php?mod=siteinfo&wid=$1;
rewrite ^/view/(\d+)\.html$ /index.php?mod=siteinfo&wid=$1;
rewrite ^/site/(\d+)-(.+)(/?)\.html$ /index.php?mod=siteinfo&wid=$1;

# 文章详情 - 支持所有4种链接结构格式
rewrite ^/artinfo-(\d+)-(.+)\.html$ /index.php?mod=artinfo&aid=$1;
rewrite ^/artinfo-(\d+)\.html$ /index.php?mod=artinfo&aid=$1;
rewrite ^/artinfo/(\d+)-(.+)\.html$ /index.php?mod=artinfo&aid=$1;
rewrite ^/artinfo/(\d+)\.html$ /index.php?mod=artinfo&aid=$1;
rewrite ^/artinfo/(\d+)/(.+)/?$ /index.php?mod=artinfo&aid=$1;
rewrite ^/artinfo/(\d+)/?$ /index.php?mod=artinfo&aid=$1;
rewrite ^/article/(\d+)-(.+)/?$ /index.php?mod=artinfo&aid=$1;
rewrite ^/article/(\d+)/?$ /index.php?mod=artinfo&aid=$1;

# 链接详情 - 支持所有4种链接结构格式
rewrite ^/linkinfo-(\d+)-(.+)\.html$ /index.php?mod=linkinfo&lid=$1;
rewrite ^/linkinfo-(\d+)\.html$ /index.php?mod=linkinfo&lid=$1;
rewrite ^/linkinfo/(\d+)-(.+)\.html$ /index.php?mod=linkinfo&lid=$1;
rewrite ^/linkinfo/(\d+)\.html$ /index.php?mod=linkinfo&lid=$1;
rewrite ^/linkinfo/(\d+)/(.+)/?$ /index.php?mod=linkinfo&lid=$1;
rewrite ^/linkinfo/(\d+)/?$ /index.php?mod=linkinfo&lid=$1;

# 自定义页面 - 支持所有4种链接结构格式
rewrite ^/diypage-(\d+)\.html$ /index.php?mod=diypage&pid=$1;
rewrite ^/diypage/(\d+)\.html$ /index.php?mod=diypage&pid=$1;
rewrite ^/diypage/(\d+)/?$ /index.php?mod=diypage&pid=$1;

# 新增模块详细页面路由 - 支持所有4种链接结构格式
rewrite ^/pending_detail-(\d+)\.html$ /index.php?mod=pending_detail&id=$1;
rewrite ^/blacklist_detail-(\d+)\.html$ /index.php?mod=blacklist_detail&id=$1;
rewrite ^/rejected_detail-(\d+)\.html$ /index.php?mod=rejected_detail&id=$1;
rewrite ^/vip_detail-(\d+)\.html$ /index.php?mod=vip_detail&id=$1;
rewrite ^/pending_detail/(\d+)\.html$ /index.php?mod=pending_detail&id=$1;
rewrite ^/blacklist_detail/(\d+)\.html$ /index.php?mod=blacklist_detail&id=$1;
rewrite ^/rejected_detail/(\d+)\.html$ /index.php?mod=rejected_detail&id=$1;
rewrite ^/vip_detail/(\d+)\.html$ /index.php?mod=vip_detail&id=$1;
rewrite ^/pending_detail/(\d+)/?$ /index.php?mod=pending_detail&id=$1;
rewrite ^/blacklist_detail/(\d+)/?$ /index.php?mod=blacklist_detail&id=$1;
rewrite ^/rejected_detail/(\d+)/?$ /index.php?mod=rejected_detail&id=$1;
rewrite ^/vip_detail/(\d+)/?$ /index.php?mod=vip_detail&id=$1;

# 分类目录 - 支持所有4种链接结构格式
# 复杂格式：webdir-{name}-{cid}-{page}.html
rewrite ^/webdir-(.+)-(\d+)-(\d+)\.html$ /index.php?mod=webdir&cid=$2&page=$3;
rewrite ^/webdir-(.+)-(\d+)\.html$ /index.php?mod=webdir&cid=$2;
# 简单格式：webdir-{cid}-{page}.html
rewrite ^/webdir-(\d+)-(\d+)\.html$ /index.php?mod=webdir&cid=$1&page=$2;
rewrite ^/webdir-(\d+)\.html$ /index.php?mod=webdir&cid=$1;
# 目录格式
rewrite ^/webdir/(\d+)-(\d+)\.html$ /index.php?mod=webdir&cid=$1&page=$2;
rewrite ^/webdir/(\d+)\.html$ /index.php?mod=webdir&cid=$1;
rewrite ^/webdir/(\d+)/(\d+)/?$ /index.php?mod=webdir&cid=$1&page=$2;
rewrite ^/webdir/(\d+)/?$ /index.php?mod=webdir&cid=$1;
rewrite ^/webdir/(.+)/(\d+)\.html$ /index.php?mod=webdir&cid=$2;
rewrite ^/webdir/(.+)/(\d+)-(\d+)\.html$ /index.php?mod=webdir&cid=$2&page=$3;
rewrite ^/webdir/(.+)/(\d+)-(.+)-(\d+)\.html$ /index.php?mod=webdir&cid=$2&sort=$3&page=$4;

# 友情链接 - 支持所有4种链接结构格式
# 复杂格式：weblink-{name}-{cid}-{page}.html
rewrite ^/weblink-(.+)-(\d+)-(\d+)\.html$ /index.php?mod=weblink&cid=$2&page=$3;
rewrite ^/weblink-(.+)-(\d+)\.html$ /index.php?mod=weblink&cid=$2;
# 简单格式：weblink-{cid}-{page}.html
rewrite ^/weblink-(\d+)-(\d+)\.html$ /index.php?mod=weblink&cid=$1&page=$2;
rewrite ^/weblink-(\d+)\.html$ /index.php?mod=weblink&cid=$1;
# 目录格式
rewrite ^/weblink/(\d+)-(\d+)\.html$ /index.php?mod=weblink&cid=$1&page=$2;
rewrite ^/weblink/(\d+)\.html$ /index.php?mod=weblink&cid=$1;
rewrite ^/weblink/(\d+)/(\d+)/?$ /index.php?mod=weblink&cid=$1&page=$2;
rewrite ^/weblink/(\d+)/?$ /index.php?mod=weblink&cid=$1;
rewrite ^/weblink/(.+)/(\d+)\.html$ /index.php?mod=weblink&cid=$2;
rewrite ^/weblink/(.+)/(\d+)-(\d+)\.html$ /index.php?mod=weblink&cid=$2&page=$3;
rewrite ^/weblink/(.+)/(\d+)-(.+)-(\d+)\.html$ /index.php?mod=weblink&cid=$2&sort=$3&page=$4;

# 文章分类 - 支持所有4种链接结构格式
# 复杂格式：article-{name}-{cid}-{page}.html
rewrite ^/article-(.+)-(\d+)-(\d+)\.html$ /index.php?mod=article&cid=$2&page=$3;
rewrite ^/article-(.+)-(\d+)\.html$ /index.php?mod=article&cid=$2;
# 简单格式：article-{cid}-{page}.html
rewrite ^/article-(\d+)-(\d+)\.html$ /index.php?mod=article&cid=$1&page=$2;
rewrite ^/article-(\d+)\.html$ /index.php?mod=article&cid=$1;
# 目录格式
rewrite ^/article/(\d+)/(\d+)/?$ /index.php?mod=article&cid=$1&page=$2;
rewrite ^/article/(.+)/(\d+)\.html$ /index.php?mod=article&cid=$2;
rewrite ^/article/(.+)/(\d+)-(\d+)\.html$ /index.php?mod=article&cid=$2&page=$3;

# 更新和归档
rewrite ^/update/(\d+)\.html$ /index.php?mod=update&days=$1;
rewrite ^/update/(\d+)-(\d+)\.html$ /index.php?mod=update&days=$1&page=$2;
rewrite ^/archives/(\d+)\.html$ /index.php?mod=archives&date=$1;
rewrite ^/archives/(\d+)-(\d+)\.html$ /index.php?mod=archives&date=$1&page=$2;

# 搜索 - 支持多种格式
# 格式1: search-{type}-{query}-{page}.html
rewrite ^/search-(name|url|tags|intro|br|pr|art|article)-(.+)-(\d+)\.html$ /index.php?mod=search&type=$1&query=$2&page=$3;
rewrite ^/search-(name|url|tags|intro|br|pr|art|article)-(.+)\.html$ /index.php?mod=search&type=$1&query=$2;
# 格式2: search/{type}/{query}.html
rewrite ^/search/(name|url|tags|intro|br|pr|art|article)/(.+)-(\d+)\.html$ /index.php?mod=search&type=$1&query=$2&page=$3;
rewrite ^/search/(name|url|tags|intro|br|pr|art|article)/(.+)\.html$ /index.php?mod=search&type=$1&query=$2;
# 格式4: search/{type}-{query}-{page}.html (混合格式)
rewrite ^/search/(name|url|tags|intro|br|pr|art|article)-(.+)-(\d+)\.html$ /index.php?mod=search&type=$1&query=$2&page=$3;
rewrite ^/search/(name|url|tags|intro|br|pr|art|article)-(.+)\.html$ /index.php?mod=search&type=$1&query=$2;
# 格式3: {br|pr}/{query}.html
rewrite ^/(br|pr)/(.+)-(\d+)\.html$ /index.php?mod=search&type=$1&query=$2&page=$3;
rewrite ^/(br|pr)/(.+)\.html$ /index.php?mod=search&type=$1&query=$2;

# RSS和网站地图
rewrite ^/rssfeed/(\d+)\.html$ /index.php?mod=rssfeed&cid=$1;
rewrite ^/sitemap/(\d+)\.html$ /index.php?mod=sitemap&cid=$1;
rewrite ^/rssfeed/(.+)/(\d+)\.html$ /index.php?mod=rssfeed&cid=$2;
rewrite ^/rssfeed/(.+)/(\d+)-(\d+)\.html$ /index.php?mod=rssfeed&cid=$2&page=$3;

# 新增模块列表页面路由
rewrite ^/pending/(\d+)\.html$ /index.php?mod=pending&page=$1;
rewrite ^/blacklist/(\d+)\.html$ /index.php?mod=blacklist&page=$1;
rewrite ^/rejected/(\d+)\.html$ /index.php?mod=rejected&page=$1;
rewrite ^/vip_list/(\d+)\.html$ /index.php?mod=vip_list&page=$1;

# 数据统计和API路由
rewrite ^/datastats/(.+)\.html$ /index.php?mod=datastats&type=$1;
rewrite ^/api/(.+)\.html$ /index.php?mod=api&type=$1;
rewrite ^/ajaxget/(.+)\.html$ /index.php?mod=ajaxget&type=$1;
rewrite ^/getdata/(.+)/(\d+)\.html$ /index.php?mod=getdata&type=$1&wid=$2;

# 基础模块路由（放在最后，避免冲突）
rewrite ^/(index|webdir|weblink|article|category|update|archives|top|feedback|link|rssfeed|sitemap)\.html$ /index.php?mod=$1;
rewrite ^/(index|webdir|weblink|article|category|update|archives|top|feedback|link|rssfeed|sitemap)(/?)$ /index.php?mod=$1;

# 新增模块基础路由
rewrite ^/(ajaxget|getdata|api|pending|addurl|quicksubmit|blacklist|rejected|vip_list|datastats)\.html$ /index.php?mod=$1;
rewrite ^/(ajaxget|getdata|api|pending|addurl|quicksubmit|blacklist|rejected|vip_list|datastats)(/?)$ /index.php?mod=$1;