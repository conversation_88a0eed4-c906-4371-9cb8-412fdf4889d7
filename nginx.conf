# 重定向查询参数格式到伪静态格式（SEO优化）
# 详情页面重定向（必须在基础模块之前，避免被错误匹配）
if ($args ~ "^mod=siteinfo&wid=([0-9]+)$") {
    return 301 /siteinfo-$1.html;
}
if ($args ~ "^mod=artinfo&aid=([0-9]+)$") {
    return 301 /artinfo-$1.html;
}
if ($args ~ "^mod=linkinfo&lid=([0-9]+)$") {
    return 301 /linkinfo-$1.html;
}
if ($args ~ "^mod=diypage&pid=([0-9]+)$") {
    return 301 /diypage-$1.html;
}
if ($args ~ "^mod=(pending_detail|blacklist_detail|rejected_detail|vip_detail)&id=([0-9]+)$") {
    return 301 /$1-$2.html;
}

# 基础模块重定向（放在详情页面之后）
if ($args ~ "^mod=(index|webdir|weblink|article|category|update|archives|top|feedback|link|rssfeed|sitemap|pending|blacklist|rejected|vip_list|datastats)$") {
    return 301 /$1.html;
}

# 分页重定向
if ($args ~ "^mod=(pending|blacklist|rejected|vip_list)&page=([0-9]+)$") {
    return 301 /$1-$2.html;
}
if ($args ~ "^mod=webdir&cid=([0-9]+)&page=([0-9]+)$") {
    return 301 /webdir-$1-$2.html;
}
if ($args ~ "^mod=webdir&cid=([0-9]+)$") {
    return 301 /webdir-$1.html;
}
if ($args ~ "^mod=weblink&cid=([0-9]+)&page=([0-9]+)$") {
    return 301 /weblink-$1-$2.html;
}
if ($args ~ "^mod=weblink&cid=([0-9]+)$") {
    return 301 /weblink-$1.html;
}
if ($args ~ "^mod=article&cid=([0-9]+)&page=([0-9]+)$") {
    return 301 /article-$1-$2.html;
}
if ($args ~ "^mod=article&cid=([0-9]+)$") {
    return 301 /article-$1.html;
}

# 处理查询参数中的mod参数（解决相对路径问题，不重定向）
if ($args ~ "^mod=(.+)") {
    rewrite ^.*$ /index.php?mod=$1 last;
}

# 基础模块路由 - 只支持简单格式（避免与详情页面冲突）
rewrite ^/(index|webdir|weblink|article|category|update|archives|top|feedback|link|rssfeed|sitemap)\.html$ /index.php?mod=$1 last;
rewrite ^/(index|webdir|weblink|article|category|update|archives|top|feedback|link|rssfeed|sitemap)(/?)$ /index.php?mod=$1 last;

# 新增模块基础路由 - 只支持简单格式
rewrite ^/(ajaxget|getdata|api|pending|addurl|quicksubmit|blacklist|rejected|vip_list|datastats)\.html$ /index.php?mod=$1 last;
rewrite ^/(ajaxget|getdata|api|pending|addurl|quicksubmit|blacklist|rejected|vip_list|datastats)(/?)$ /index.php?mod=$1 last;

rewrite ^/update/(\d+)\.html$ /index.php?mod=update&days=$1 last;
rewrite ^/update/(\d+)-(\d+)\.html$ /index.php?mod=update&days=$1&page=$2 last;
rewrite ^/archives/(\d+)\.html$ /index.php?mod=archives&date=$1 last;
rewrite ^/archives/(\d+)-(\d+)\.html$ /index.php?mod=archives&date=$1&page=$2 last;
# 搜索 - 支持多种格式
# 格式1: search-{type}-{query}-{page}.html
rewrite ^/search-(name|url|tags|intro|br|pr|art|article)-(.+)-(\d+)\.html$ /index.php?mod=search&type=$1&query=$2&page=$3 last;
rewrite ^/search-(name|url|tags|intro|br|pr|art|article)-(.+)\.html$ /index.php?mod=search&type=$1&query=$2 last;
# 格式2: search/{type}/{query}.html
rewrite ^/search/(name|url|tags|intro|br|pr|art|article)/(.+)-(\d+)\.html$ /index.php?mod=search&type=$1&query=$2&page=$3 last;
rewrite ^/search/(name|url|tags|intro|br|pr|art|article)/(.+)\.html$ /index.php?mod=search&type=$1&query=$2 last;
# 格式4: search/{type}-{query}-{page}.html (混合格式)
rewrite ^/search/(name|url|tags|intro|br|pr|art|article)-(.+)-(\d+)\.html$ /index.php?mod=search&type=$1&query=$2&page=$3 last;
rewrite ^/search/(name|url|tags|intro|br|pr|art|article)-(.+)\.html$ /index.php?mod=search&type=$1&query=$2 last;
# 格式3: {br|pr}/{query}.html
rewrite ^/(br|pr)/(.+)-(\d+)\.html$ /index.php?mod=search&type=$1&query=$2&page=$3 last;
rewrite ^/(br|pr)/(.+)\.html$ /index.php?mod=search&type=$1&query=$2 last;
# 网站详情 - 支持所有4种链接结构格式
# 格式1: 默认 - ?mod=siteinfo&wid=481 (由查询参数处理规则处理)
# 格式2: 文件型 - siteinfo-481.html 或 siteinfo-481-name-domain.html
rewrite ^/siteinfo-(\d+)-(.+)\.html$ /index.php?mod=siteinfo&wid=$1 last;
rewrite ^/siteinfo-(\d+)\.html$ /index.php?mod=siteinfo&wid=$1 last;
# 格式3: 目录和文件型 - siteinfo/481.html 或 siteinfo/481-name-domain.html
rewrite ^/siteinfo/(\d+)-(.+)\.html$ /index.php?mod=siteinfo&wid=$1 last;
rewrite ^/siteinfo/(\d+)\.html$ /index.php?mod=siteinfo&wid=$1 last;
# 格式4: 目录型 - siteinfo/481/name/domain 或 siteinfo/481/name 或 siteinfo/481
rewrite ^/siteinfo/(\d+)/(.+)/(.+)/?$ /index.php?mod=siteinfo&wid=$1 last;
rewrite ^/siteinfo/(\d+)/(.+)/?$ /index.php?mod=siteinfo&wid=$1 last;
rewrite ^/siteinfo/(\d+)/?$ /index.php?mod=siteinfo&wid=$1 last;
# 兼容原有格式
rewrite ^/view/(\d+)\.html$ /index.php?mod=siteinfo&wid=$1 last;
rewrite ^/site/(\d+)-(.+)(/?)\.html$ /index.php?mod=siteinfo&wid=$1 last;
# 文章详情 - 支持所有4种链接结构格式
# 格式2: 文件型 - artinfo-157.html 或 artinfo-157-title.html
rewrite ^/artinfo-(\d+)-(.+)\.html$ /index.php?mod=artinfo&aid=$1 last;
rewrite ^/artinfo-(\d+)\.html$ /index.php?mod=artinfo&aid=$1 last;
# 格式3: 目录和文件型 - artinfo/157.html 或 artinfo/157-title.html
rewrite ^/artinfo/(\d+)-(.+)\.html$ /index.php?mod=artinfo&aid=$1 last;
rewrite ^/artinfo/(\d+)\.html$ /index.php?mod=artinfo&aid=$1 last;
# 格式4: 目录型 - artinfo/157/title 或 artinfo/157
rewrite ^/artinfo/(\d+)/(.+)/?$ /index.php?mod=artinfo&aid=$1 last;
rewrite ^/artinfo/(\d+)/?$ /index.php?mod=artinfo&aid=$1 last;
# 兼容原有格式
rewrite ^/article/(\d+)-(.+)/?$ /index.php?mod=artinfo&aid=$1 last;
rewrite ^/article/(\d+)/?$ /index.php?mod=artinfo&aid=$1 last;
# 链接详情 - 支持所有4种链接结构格式
# 格式2: 文件型 - linkinfo-123.html 或 linkinfo-123-title.html
rewrite ^/linkinfo-(\d+)-(.+)\.html$ /index.php?mod=linkinfo&lid=$1 last;
rewrite ^/linkinfo-(\d+)\.html$ /index.php?mod=linkinfo&lid=$1 last;
# 格式3: 目录和文件型 - linkinfo/123.html 或 linkinfo/123-title.html
rewrite ^/linkinfo/(\d+)-(.+)\.html$ /index.php?mod=linkinfo&lid=$1 last;
rewrite ^/linkinfo/(\d+)\.html$ /index.php?mod=linkinfo&lid=$1 last;
# 格式4: 目录型 - linkinfo/123/title 或 linkinfo/123
rewrite ^/linkinfo/(\d+)/(.+)/?$ /index.php?mod=linkinfo&lid=$1 last;
rewrite ^/linkinfo/(\d+)/?$ /index.php?mod=linkinfo&lid=$1 last;
# 自定义页面 - 支持所有4种链接结构格式
# 格式2: 文件型 - diypage-1.html
rewrite ^/diypage-(\d+)\.html$ /index.php?mod=diypage&pid=$1 last;
# 格式3: 目录和文件型 - diypage/1.html
rewrite ^/diypage/(\d+)\.html$ /index.php?mod=diypage&pid=$1 last;
# 格式4: 目录型 - diypage/1
rewrite ^/diypage/(\d+)/?$ /index.php?mod=diypage&pid=$1 last;
# 分类目录 - 支持所有4种链接结构格式
# 复杂格式：webdir-{name}-{cid}-{page}.html
rewrite ^/webdir-(.+)-(\d+)-(\d+)\.html$ /index.php?mod=webdir&cid=$2&page=$3 last;
rewrite ^/webdir-(.+)-(\d+)\.html$ /index.php?mod=webdir&cid=$2 last;
# 简单格式：webdir-{cid}-{page}.html
rewrite ^/webdir-(\d+)-(\d+)\.html$ /index.php?mod=webdir&cid=$1&page=$2 last;
rewrite ^/webdir-(\d+)\.html$ /index.php?mod=webdir&cid=$1 last;
# 目录格式
rewrite ^/webdir/(\d+)-(\d+)\.html$ /index.php?mod=webdir&cid=$1&page=$2 last;
rewrite ^/webdir/(\d+)\.html$ /index.php?mod=webdir&cid=$1 last;
rewrite ^/webdir/(\d+)/(\d+)/?$ /index.php?mod=webdir&cid=$1&page=$2 last;
rewrite ^/webdir/(\d+)/?$ /index.php?mod=webdir&cid=$1 last;

# 友情链接 - 支持所有4种链接结构格式
# 复杂格式：weblink-{name}-{cid}-{page}.html
rewrite ^/weblink-(.+)-(\d+)-(\d+)\.html$ /index.php?mod=weblink&cid=$2&page=$3 last;
rewrite ^/weblink-(.+)-(\d+)\.html$ /index.php?mod=weblink&cid=$2 last;
# 简单格式：weblink-{cid}-{page}.html
rewrite ^/weblink-(\d+)-(\d+)\.html$ /index.php?mod=weblink&cid=$1&page=$2 last;
rewrite ^/weblink-(\d+)\.html$ /index.php?mod=weblink&cid=$1 last;
# 目录格式
rewrite ^/weblink/(\d+)-(\d+)\.html$ /index.php?mod=weblink&cid=$1&page=$2 last;
rewrite ^/weblink/(\d+)\.html$ /index.php?mod=weblink&cid=$1 last;
rewrite ^/weblink/(\d+)/(\d+)/?$ /index.php?mod=weblink&cid=$1&page=$2 last;
rewrite ^/weblink/(\d+)/?$ /index.php?mod=weblink&cid=$1 last;

# 文章分类 - 支持所有4种链接结构格式
# 复杂格式：article-{name}-{cid}-{page}.html
rewrite ^/article-(.+)-(\d+)-(\d+)\.html$ /index.php?mod=article&cid=$2&page=$3 last;
rewrite ^/article-(.+)-(\d+)\.html$ /index.php?mod=article&cid=$2 last;
# 简单格式：article-{cid}-{page}.html
rewrite ^/article-(\d+)-(\d+)\.html$ /index.php?mod=article&cid=$1&page=$2 last;
rewrite ^/article-(\d+)\.html$ /index.php?mod=article&cid=$1 last;
# 目录格式
rewrite ^/article/(\d+)/(\d+)/?$ /index.php?mod=article&cid=$1&page=$2 last;

# RSS和网站地图
rewrite ^/rssfeed/(\d+)\.html$ /index.php?mod=rssfeed&cid=$1 last;
rewrite ^/sitemap/(\d+)\.html$ /index.php?mod=sitemap&cid=$1 last;

# 兼容原有格式
rewrite ^/webdir/(.+)/(\d+)\.html$ /index.php?mod=webdir&cid=$2 last;
rewrite ^/webdir/(.+)/(\d+)-(\d+)\.html$ /index.php?mod=webdir&cid=$2&page=$3 last;
rewrite ^/webdir/(.+)/(\d+)-(.+)-(\d+)\.html$ /index.php?mod=webdir&cid=$2&sort=$3&page=$4 last;
rewrite ^/weblink/(.+)/(\d+)\.html$ /index.php?mod=weblink&cid=$2 last;
rewrite ^/weblink/(.+)/(\d+)-(\d+)\.html$ /index.php?mod=weblink&cid=$2&page=$3 last;
rewrite ^/weblink/(.+)/(\d+)-(.+)-(\d+)\.html$ /index.php?mod=weblink&cid=$2&sort=$3&page=$4 last;
rewrite ^/article/(.+)/(\d+)\.html$ /index.php?mod=article&cid=$2 last;
rewrite ^/article/(.+)/(\d+)-(\d+)\.html$ /index.php?mod=article&cid=$2&page=$3 last;
rewrite ^/rssfeed/(.+)/(\d+)\.html$ /index.php?mod=rssfeed&cid=$2 last;
rewrite ^/rssfeed/(.+)/(\d+)-(\d+)\.html$ /index.php?mod=rssfeed&cid=$2&page=$3 last;

# 新增模块详细页面路由 - 支持所有4种链接结构格式
# 格式2: 文件型 - pending_detail-123.html
rewrite ^/pending_detail-(\d+)\.html$ /index.php?mod=pending_detail&id=$1 last;
rewrite ^/blacklist_detail-(\d+)\.html$ /index.php?mod=blacklist_detail&id=$1 last;
rewrite ^/rejected_detail-(\d+)\.html$ /index.php?mod=rejected_detail&id=$1 last;
rewrite ^/vip_detail-(\d+)\.html$ /index.php?mod=vip_detail&id=$1 last;
# 格式3: 目录和文件型 - pending_detail/123.html
rewrite ^/pending_detail/(\d+)\.html$ /index.php?mod=pending_detail&id=$1 last;
rewrite ^/blacklist_detail/(\d+)\.html$ /index.php?mod=blacklist_detail&id=$1 last;
rewrite ^/rejected_detail/(\d+)\.html$ /index.php?mod=rejected_detail&id=$1 last;
rewrite ^/vip_detail/(\d+)\.html$ /index.php?mod=vip_detail&id=$1 last;
# 格式4: 目录型 - pending_detail/123
rewrite ^/pending_detail/(\d+)/?$ /index.php?mod=pending_detail&id=$1 last;
rewrite ^/blacklist_detail/(\d+)/?$ /index.php?mod=blacklist_detail&id=$1 last;
rewrite ^/rejected_detail/(\d+)/?$ /index.php?mod=rejected_detail&id=$1 last;
rewrite ^/vip_detail/(\d+)/?$ /index.php?mod=vip_detail&id=$1 last;

# 新增模块列表页面路由
rewrite ^/pending/(\d+)\.html$ /index.php?mod=pending&page=$1 last;
rewrite ^/blacklist/(\d+)\.html$ /index.php?mod=blacklist&page=$1 last;
rewrite ^/rejected/(\d+)\.html$ /index.php?mod=rejected&page=$1 last;
rewrite ^/vip_list/(\d+)\.html$ /index.php?mod=vip_list&page=$1 last;

# 数据统计和API路由
rewrite ^/datastats/(.+)\.html$ /index.php?mod=datastats&type=$1 last;
rewrite ^/api/(.+)\.html$ /index.php?mod=api&type=$1 last;
rewrite ^/ajaxget/(.+)\.html$ /index.php?mod=ajaxget&type=$1 last;
rewrite ^/getdata/(.+)/(\d+)\.html$ /index.php?mod=getdata&type=$1&wid=$2 last;