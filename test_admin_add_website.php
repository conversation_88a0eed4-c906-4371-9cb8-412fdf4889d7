<?php
/**
 * 测试管理员添加网站自动评论功能
 */

// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置基本常量
define('IN_IWEBDIR', TRUE);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');

// 引入必要文件
require_once(ROOT_PATH.'source/init.php');
require_once(ROOT_PATH.'module/auto_comments.php');

echo "<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <title>测试管理员添加网站自动评论</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; padding: 10px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; padding: 10px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; margin: 10px 0; }
        .info { color: #0c5460; padding: 10px; background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 4px; margin: 10px 0; }
        .warning { color: #856404; padding: 10px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; margin: 10px 0; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 10px 5px 0 0; }
        .btn:hover { background: #0056b3; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 10px; border: 1px solid #ddd; text-align: left; }
        th { background: #f8f9fa; }
        .test-section { margin: 20px 0; padding: 15px; border-left: 4px solid #007bff; background: #f8f9fa; }
    </style>
</head>
<body>
<div class='container'>";

echo "<h1>🧪 测试管理员添加网站自动评论功能</h1>";

try {
    // 检查数据库连接
    if (!$DB || !$DB->db_link) {
        throw new Exception("数据库连接失败");
    }
    
    echo "<div class='success'>✅ 数据库连接正常</div>";
    
    // 1. 检查自动评论配置
    echo "<div class='test-section'>";
    echo "<h2>1. 检查自动评论配置</h2>";
    
    $config = get_auto_comment_config();
    if ($config['enabled']) {
        echo "<div class='success'>✅ 自动评论功能已启用</div>";
        echo "<div class='info'>评论数量: {$config['comment_count']} 条</div>";
    } else {
        echo "<div class='error'>❌ 自动评论功能未启用，请先在后台启用</div>";
    }
    echo "</div>";
    
    // 2. 模拟管理员添加网站流程
    echo "<div class='test-section'>";
    echo "<h2>2. 模拟管理员添加网站流程</h2>";
    
    if (isset($_GET['action']) && $_GET['action'] == 'simulate_add') {
        echo "<div class='info'>正在模拟管理员添加网站...</div>";
        
        // 模拟添加网站数据
        $test_website_data = array(
            'cate_id' => 1,
            'web_name' => '测试网站' . date('His'),
            'web_url' => 'http://test' . date('His') . '.example.com',
            'web_tags' => '测试,网站',
            'web_intro' => '这是一个测试网站，用于验证自动评论功能',
            'web_owner' => '测试管理员',
            'web_email' => '<EMAIL>',
            'web_pic' => 'themes/default/skin/wait.png',
            'web_status' => 3, // 管理员添加直接设为已审核
            'web_ctime' => time(),
            'web_utime' => time(),
            'web_ip' => '127.0.0.1',
            'web_grank' => 0,
            'web_brank' => 0,
            'web_srank' => 0,
            'web_arank' => 0,
            'user_id' => 1 // 假设管理员ID为1
        );
        
        $website_table = $DB->table('websites');
        
        // 检查是否已存在相同URL的测试网站
        $existing = $DB->fetch_one("SELECT web_id FROM $website_table WHERE web_url = '{$test_website_data['web_url']}'");
        if ($existing) {
            echo "<div class='warning'>⚠️ 测试网站已存在，删除后重新添加</div>";
            $DB->delete($website_table, "web_id = {$existing['web_id']}");
            
            // 同时删除相关评论
            $comment_table = $DB->table('website_comments');
            $DB->delete($comment_table, "web_id = {$existing['web_id']}");
        }
        
        // 插入测试网站
        $result = $DB->insert($website_table, $test_website_data);
        if ($result) {
            $web_id = $DB->insert_id();
            echo "<div class='success'>✅ 测试网站添加成功，ID: {$web_id}</div>";
            
            // 插入统计数据
            $stat_data = array(
                'web_id' => $web_id,
                'web_ip' => '127.0.0.1',
                'web_grank' => 0,
                'web_brank' => 0,
                'web_srank' => 0,
                'web_arank' => 0,
                'web_instat' => 0,
                'web_outstat' => 0,
                'web_views' => 0,
                'web_errors' => 0,
                'web_ctime' => time(),
                'web_utime' => time()
            );
            $DB->insert($DB->table('webdata'), $stat_data);
            
            // 模拟管理员添加网站时的自动评论逻辑
            if ($test_website_data['web_status'] == 3) {
                echo "<div class='info'>网站状态为已审核，触发自动评论...</div>";
                
                try {
                    if (is_auto_comment_enabled()) {
                        $config = get_auto_comment_config();
                        $comment_result = auto_add_website_comments($web_id, $config['comment_count']);
                        
                        if ($comment_result['success']) {
                            echo "<div class='success'>✅ " . $comment_result['message'] . "</div>";
                            
                            // 显示添加的评论
                            $comment_table = $DB->table('website_comments');
                            $query = $DB->query("SELECT * FROM $comment_table WHERE web_id = $web_id ORDER BY create_time DESC");
                            
                            if ($DB->num_rows($query) > 0) {
                                echo "<h3>添加的评论：</h3>";
                                echo "<table>";
                                echo "<tr><th>用户</th><th>评分</th><th>评论内容</th><th>时间</th></tr>";
                                
                                while ($comment = $DB->fetch_array($query)) {
                                    echo "<tr>";
                                    echo "<td>" . htmlspecialchars($comment['user_name']) . "</td>";
                                    echo "<td>{$comment['content_quality']}★ {$comment['service_quality']}★ {$comment['trust_level']}★</td>";
                                    echo "<td>" . htmlspecialchars($comment['comment_content']) . "</td>";
                                    echo "<td>" . date('Y-m-d H:i:s', $comment['create_time']) . "</td>";
                                    echo "</tr>";
                                }
                                echo "</table>";
                            } else {
                                echo "<div class='error'>❌ 没有找到添加的评论</div>";
                            }
                        } else {
                            echo "<div class='error'>❌ " . $comment_result['message'] . "</div>";
                        }
                    } else {
                        echo "<div class='warning'>⚠️ 自动评论功能未启用</div>";
                    }
                } catch (Exception $e) {
                    echo "<div class='error'>❌ 自动评论添加失败: " . $e->getMessage() . "</div>";
                }
            }
            
            echo "<div style='margin-top: 20px;'>";
            echo "<a href='siteinfo.php?web_id={$web_id}' class='btn' target='_blank'>查看网站详情页</a>";
            echo "<a href='?action=cleanup&web_id={$web_id}' class='btn'>清理测试数据</a>";
            echo "</div>";
            
        } else {
            echo "<div class='error'>❌ 测试网站添加失败</div>";
        }
    }
    
    // 3. 清理测试数据
    if (isset($_GET['action']) && $_GET['action'] == 'cleanup' && isset($_GET['web_id'])) {
        $web_id = intval($_GET['web_id']);
        
        echo "<div class='info'>正在清理测试数据...</div>";
        
        // 删除评论
        $comment_table = $DB->table('website_comments');
        $comment_count = $DB->get_count($comment_table, "web_id = $web_id");
        $DB->delete($comment_table, "web_id = $web_id");
        
        // 删除统计数据
        $DB->delete($DB->table('webdata'), "web_id = $web_id");
        
        // 删除网站
        $website_table = $DB->table('websites');
        $DB->delete($website_table, "web_id = $web_id");
        
        echo "<div class='success'>✅ 清理完成：删除了网站和 {$comment_count} 条评论</div>";
    }
    
    echo "</div>";
    
    // 4. 检查最近添加的网站
    echo "<div class='test-section'>";
    echo "<h2>3. 检查最近添加的网站</h2>";
    
    $website_table = $DB->table('websites');
    $recent_websites = $DB->fetch_all("SELECT web_id, web_name, web_url, web_status, web_ctime FROM $website_table WHERE web_status = 3 ORDER BY web_id DESC LIMIT 5");
    
    if (!empty($recent_websites)) {
        echo "<table>";
        echo "<tr><th>网站ID</th><th>网站名称</th><th>状态</th><th>评论数</th><th>添加时间</th></tr>";
        
        foreach ($recent_websites as $website) {
            $comment_table = $DB->table('website_comments');
            $comment_count = $DB->get_count($comment_table, "web_id = {$website['web_id']}");
            
            echo "<tr>";
            echo "<td>{$website['web_id']}</td>";
            echo "<td>" . htmlspecialchars($website['web_name']) . "</td>";
            echo "<td>已审核</td>";
            echo "<td>{$comment_count} 条</td>";
            echo "<td>" . date('Y-m-d H:i:s', $website['web_ctime']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<div class='info'>暂无已审核的网站</div>";
    }
    echo "</div>";
    
    // 操作按钮
    echo "<div style='margin-top: 30px; text-align: center;'>";
    if (!isset($_GET['action'])) {
        echo "<a href='?action=simulate_add' class='btn'>🧪 模拟管理员添加网站</a>";
    }
    echo "<a href='debug_auto_comments.php' class='btn'>🔍 完整调试</a>";
    echo "<a href='system/option.php?opt=comment' class='btn' target='_blank'>⚙️ 后台配置</a>";
    echo "</div>";
    
    // 说明信息
    echo "<div class='info' style='margin-top: 30px;'>";
    echo "<h3>💡 功能说明</h3>";
    echo "<ul>";
    echo "<li><strong>管理员添加网站</strong>：网站状态直接设为已审核(3)，会立即触发自动评论</li>";
    echo "<li><strong>前台用户提交</strong>：网站状态为待审核(2)，管理员审核通过时触发自动评论</li>";
    echo "<li><strong>状态变更审核</strong>：网站状态从待审核(2)变为已审核(3)时触发自动评论</li>";
    echo "<li><strong>避免重复</strong>：系统会检查是否已有自动评论，避免重复添加</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ 测试失败</h2>";
    echo "<p>错误信息: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "</div></body></html>";
?>
