<?php
// 测试搜索重写规则修复
echo "<h2>搜索重写规则修复测试</h2>";

// 测试URL列表
$test_urls = array(
    '/search/article-whois-1.html' => '问题URL - 文章搜索带分页',
    '/search/article/whois.html' => '文章搜索不带分页',
    '/search-article-whois-1.html' => '格式1 - 文章搜索带分页',
    '/search-article-whois.html' => '格式1 - 文章搜索不带分页',
    '/search/name/test.html' => '网站名称搜索',
    '/search/url/example.html' => '网站URL搜索',
    '/search/tags/php-2.html' => '标签搜索带分页',
    '/search/intro/description.html' => '介绍搜索',
    '/br/test.html' => '百度权重搜索',
    '/pr/example-3.html' => 'PageRank搜索带分页',
    '/search/art/tutorial.html' => 'art类型文章搜索',
    '/search-art-guide-2.html' => 'art类型搜索格式1'
);

echo "<h3>测试URL列表：</h3>";
echo "<table border='1' cellpadding='5' cellspacing='0'>";
echo "<tr><th>URL</th><th>说明</th><th>测试</th></tr>";
foreach ($test_urls as $url => $desc) {
    echo "<tr>";
    echo "<td><code>" . $url . "</code></td>";
    echo "<td>" . $desc . "</td>";
    echo "<td><a href='" . $url . "' target='_blank'>测试</a></td>";
    echo "</tr>";
}
echo "</table>";

echo "<h3>修复内容总结：</h3>";
echo "<div style='background:#f0f8ff; padding:15px; border-left:4px solid #0066cc;'>";
echo "<h4>1. httpd.ini 修复：</h4>";
echo "<ul>";
echo "<li>✅ 添加了 <code>article</code> 和 <code>art</code> 类型支持</li>";
echo "<li>✅ 添加了 <code>br</code> 和 <code>pr</code> 类型支持</li>";
echo "<li>✅ 支持3种URL格式：search-type-query.html, search/type/query.html, br/query.html</li>";
echo "</ul>";

echo "<h4>2. search.php 模块修复：</h4>";
echo "<ul>";
echo "<li>✅ 添加了对 <code>art</code> 类型的支持（等同于article）</li>";
echo "<li>✅ 添加了对 <code>br</code> 和 <code>pr</code> 类型的搜索逻辑</li>";
echo "</ul>";
echo "</div>";

echo "<h3>支持的搜索类型：</h3>";
echo "<ul>";
echo "<li><strong>name:</strong> 按网站名称搜索</li>";
echo "<li><strong>url:</strong> 按网站URL搜索</li>";
echo "<li><strong>tags:</strong> 按标签搜索</li>";
echo "<li><strong>intro:</strong> 按介绍搜索</li>";
echo "<li><strong>article/art:</strong> 文章搜索</li>";
echo "<li><strong>br:</strong> 百度权重相关搜索</li>";
echo "<li><strong>pr:</strong> PageRank相关搜索</li>";
echo "</ul>";

echo "<h3>原问题URL修复状态：</h3>";
echo "<div style='background:#f0fff0; padding:15px; border-left:4px solid #00cc66;'>";
echo "<p><strong>问题URL:</strong> <code>/search/article-whois-1.html</code></p>";
echo "<p><strong>状态:</strong> ✅ <span style='color:green; font-weight:bold;'>已修复</span></p>";
echo "<p><strong>匹配规则:</strong> <code>^/search/(name|url|tags|intro|br|pr|art|article)/(.+)-(\d+)\.html$</code></p>";
echo "<p><strong>解析结果:</strong> <code>mod=search&type=article&query=whois&page=1</code></p>";
echo "<p><strong>处理模块:</strong> module/search.php (支持article类型文章搜索)</p>";
echo "</div>";

// 显示当前GET参数（如果有的话）
if (!empty($_GET)) {
    echo "<h3>当前GET参数：</h3>";
    echo "<pre style='background:#f5f5f5; padding:10px; border:1px solid #ddd;'>";
    print_r($_GET);
    echo "</pre>";

    // 如果是搜索请求，显示搜索信息
    if (isset($_GET['mod']) && $_GET['mod'] == 'search') {
        echo "<h4>搜索信息解析：</h4>";
        echo "<ul>";
        echo "<li><strong>模块:</strong> " . htmlspecialchars($_GET['mod']) . "</li>";
        if (isset($_GET['type'])) echo "<li><strong>搜索类型:</strong> " . htmlspecialchars($_GET['type']) . "</li>";
        if (isset($_GET['query'])) echo "<li><strong>搜索关键词:</strong> " . htmlspecialchars($_GET['query']) . "</li>";
        if (isset($_GET['page'])) echo "<li><strong>页码:</strong> " . htmlspecialchars($_GET['page']) . "</li>";
        echo "</ul>";
    }
}

echo "<hr>";
echo "<p><small>测试时间: " . date('Y-m-d H:i:s') . "</small></p>";
?>
