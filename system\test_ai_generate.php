<?php
// test_ai_generate.php - 测试AI生成功能
header('Content-Type: text/html; charset=utf-8');

// 引入系统配置
define('IN_ADMIN', TRUE);
define('ROOT_PATH', str_replace('\\', '/', dirname(dirname(__FILE__))).'/');
define('APP_PATH', ROOT_PATH.'source/');
require(APP_PATH.'init.php');
require(APP_PATH.'module/static.php');

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>AI生成功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"], textarea { width: 400px; padding: 5px; }
        button { padding: 8px 15px; background: #007cba; color: white; border: none; cursor: pointer; }
        button:hover { background: #005a87; }
        .result { margin-top: 20px; padding: 10px; border: 1px solid #ddd; background: #f9f9f9; }
        .status { margin-left: 10px; }
        .success { color: #009900; }
        .error { color: #cc0000; }
        .loading { color: #0066cc; }
    </style>
</head>
<body>
    <h1>AI生成关键词和简介测试</h1>
    
    <form id="testForm">
        <div class="form-group">
            <label for="url">网站域名：</label>
            <input type="text" id="url" name="url" placeholder="例：www.example.com" value="www.baidu.com">
        </div>
        
        <div class="form-group">
            <label for="meta_title">网站标题（可选）：</label>
            <input type="text" id="meta_title" name="meta_title" placeholder="网站标题" value="百度一下，你就知道">
        </div>
        
        <div class="form-group">
            <label for="meta_keywords">Meta关键词（可选）：</label>
            <input type="text" id="meta_keywords" name="meta_keywords" placeholder="关键词1,关键词2" value="搜索引擎,百度,搜索">
        </div>
        
        <div class="form-group">
            <label for="meta_description">Meta描述（可选）：</label>
            <textarea id="meta_description" name="meta_description" rows="3" placeholder="网站描述">全球领先的中文搜索引擎、致力于让网民更便捷地获取信息，找到所求。百度超过千亿的中文网页数据库，可以瞬间找到相关的搜索结果。</textarea>
        </div>
        
        <button type="button" onclick="testGenerate()">测试生成AI关键词和简介</button>
        <span id="status" class="status"></span>
    </form>
    
    <div id="result" class="result" style="display: none;">
        <h3>生成结果：</h3>
        <div id="result-content"></div>
    </div>

    <script>
        function testGenerate() {
            var url = document.getElementById('url').value;
            var metaTitle = document.getElementById('meta_title').value;
            var metaKeywords = document.getElementById('meta_keywords').value;
            var metaDescription = document.getElementById('meta_description').value;
            
            if (!url) {
                alert('请输入网站域名');
                return;
            }
            
            // 显示加载状态
            document.getElementById('status').innerHTML = '<span class="loading">正在生成中...</span>';
            document.getElementById('result').style.display = 'none';
            
            // 发送AJAX请求
            var xhr = new XMLHttpRequest();
            xhr.open('POST', 'generate_intro.php', true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        try {
                            var response = JSON.parse(xhr.responseText);
                            if (response.status === 'success') {
                                document.getElementById('status').innerHTML = '<span class="success">生成成功！</span>';
                                
                                var resultHtml = '<p><strong>生成的关键词：</strong>' + (response.keywords || '无') + '</p>';
                                resultHtml += '<p><strong>生成的简介：</strong>' + (response.intro || '无') + '</p>';
                                resultHtml += '<p><strong>原始返回内容：</strong></p><pre>' + (response.raw_content || '无') + '</pre>';
                                
                                document.getElementById('result-content').innerHTML = resultHtml;
                                document.getElementById('result').style.display = 'block';
                            } else {
                                document.getElementById('status').innerHTML = '<span class="error">生成失败：' + response.message + '</span>';
                            }
                        } catch (e) {
                            document.getElementById('status').innerHTML = '<span class="error">JSON解析错误：' + e.message + '</span>';
                            console.log('JSON解析错误:', e);
                            console.log('原始响应:', xhr.responseText);
                        }
                    } else {
                        document.getElementById('status').innerHTML = '<span class="error">请求错误(' + xhr.status + ')，请重试</span>';
                        console.log('HTTP错误:', xhr.status, xhr.statusText);
                        console.log('响应内容:', xhr.responseText);
                    }
                }
            };
            
            // 构建POST数据
            var postData = 'action=generate_keywords_intro' +
                          '&url=' + encodeURIComponent(url) +
                          '&meta_title=' + encodeURIComponent(metaTitle) +
                          '&meta_keywords=' + encodeURIComponent(metaKeywords) +
                          '&meta_description=' + encodeURIComponent(metaDescription);
            
            xhr.send(postData);
        }
    </script>
</body>
</html>
